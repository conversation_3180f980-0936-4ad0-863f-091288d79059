// Core Node.js imports
import path from 'path';
import fs from 'fs/promises';
import delay from 'delay';
import { fileExistsAtPath } from '@/util/fs';

// Internal imports
import { AgentManager } from '../agent';
import { SayTool } from '../types/type';
import { HandleError, AskApproval, PushToolResult, RemoveClosingTag, ToolUse, ReportToolAction } from '../types/message';
import { getReadablePath } from '@/util/path';
import { ASSISTANT_NAMESPACE } from '@/util/const';
import { ReportCodeGenerate } from '../types/tool';

export async function writeToFileTool(
  agent: AgentManager,
  block: ToolUse,
  askApproval: AskApproval,
  handleError: HandleError,
  pushToolResult: PushToolResult,
  removeClosingTag: RemoveClosingTag,
  reportToolAction: ReportToolAction,
) {
  const relPath: string | undefined = block.params.path;
  let newContent: string | undefined = block.params.content;

  // pre-processing newContent for cases where weaker models might add artifacts like markdown codeblock markers (deepseek/llama) or extra escape characters (gemini)
  // if (newContent.startsWith('```')) {
  //   // agent handles cases where it includes language specifiers like ```python ```js
  //   newContent = newContent.split('\n').slice(1).join('\n').trim();
  // }

  // if (newContent.endsWith('```')) {
  //   newContent = newContent.split('\n').slice(0, -1).join('\n').trim();
  // }

  const sharedMessageProps: SayTool = {
    tool: 'editFile',
    path: getReadablePath(agent.cwd, removeClosingTag('path', relPath)),
    content: '',
    tool_version: 'v2'
  };

  try {
    if (block.partial) {
      // update gui message
      const partialMessage = JSON.stringify(sharedMessageProps);
      await agent.ask('tool', partialMessage, block.partial).catch(() => {});

      return;
    } else {
      if (!relPath) {
        agent.consecutiveMistakeCount++;
        pushToolResult(await agent.sayAndCreateMissingParamError('write_to_file', 'path'));
        return;
      }

      if (!newContent) {
        agent.consecutiveMistakeCount++;
        pushToolResult(await agent.sayAndCreateMissingParamError('write_to_file', 'content'));
        return;
      }
      // Determine if the path is outside the workspace
      const fullPath = relPath ? path.resolve(agent.cwd, removeClosingTag('path', relPath)) : '';
      const fileExists = await fileExistsAtPath(fullPath);
      // if (fileExists) {
      //   agent.consecutiveMistakeCount++;
      //   const formattedError = `File has existed at path: ${fullPath}\n You can't use this tool to edit a file that existed. Use the replace_in_file tool to edit existing files instead.`;
      //   pushToolResult(formattedError);
      //   return;
      // }

      agent.consecutiveMistakeCount = 0;
      agent.logger.reportUserAction({
        key: 'agent_tools_request',
        type: 'write_to_file'
      });
      const startToolTime = Date.now();

      let generationCall = agent.trace?.generation({
        name: 'tool_call',
        input: {
          path: removeClosingTag('path', relPath),
          newContent
        },
        metadata: {
          name: block.name
        }
      });

      agent.removeLastPartialMessageIfExistsWithType('ask', 'tool');
      await agent.say('tool', JSON.stringify({ ...sharedMessageProps, content: newContent }), block.partial);
      await delay(1000); // wait for diff view to update

      const { data } = await agent.messenger.request('assistant/agent/writeToFile', {
        path: relPath,
        newFile: !fileExists,
        content: newContent
      });
      agent.reportGenerateCode([
        {
          filePath: relPath,
          replace: newContent,
          search: ''
        }
      ]);

      pushToolResult(data?.content || '');

      reportToolAction(Date.now() - startToolTime, {
        contentLength: newContent.length,
        noModified: !!data?.noModified,
        lines: newContent.split('\n').length,
        type: data?.type
      });

      generationCall?.end({
        output: { type: data?.type, content: data?.content }
      });
      agent.logger.perf({
        namespace: ASSISTANT_NAMESPACE,
        subtag: 'kwaipilot-ide-agent-chat-tool',
        millis: Date.now() - startToolTime,
        extra4: data?.type === 'success' ? 'success' : 'error',
        extra6: block.name
      });

      await agent.saveCheckpoint();

      return;
    }
  } catch (error: any) {
    await handleError('writing file', error, 'write_to_file');
    return;
  }
}
